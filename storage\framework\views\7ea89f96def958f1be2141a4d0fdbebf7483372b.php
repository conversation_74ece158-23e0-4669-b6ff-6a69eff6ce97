<?php $__env->startSection('title', 'ติดต่อเรา - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Hero Section Styles */
.hero-section {
    min-height: 40vh;
    position: relative;
    overflow: hidden;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed;
    filter: brightness(0.8) contrast(1.1) saturate(1.2);
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.5) 0%,
        rgba(139, 69, 19, 0.7) 30%,
        rgba(218, 165, 32, 0.4) 70%,
        rgba(0, 0, 0, 0.6) 100%
    );
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
    height: 40vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.fade-in-up {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Button Styles */
.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
    }

    .hero-section .container {
        height: 50vh;
    }

    .display-2 {
        font-size: 2.5rem;
    }

    .fs-3 {
        font-size: 1.2rem !important;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<?php
    $backgroundImage = $contactPage->hero_image
        ? asset('storage/' . $contactPage->hero_image)
        : ($contactPage->default_background
            ? asset('storage/' . $contactPage->default_background)
            : asset('images/restaurant/background.jpg'));
?>
<section class="hero-section">
    <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('<?php echo e($backgroundImage); ?>'); background-size: cover; background-position: center;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-10 mx-auto text-center">
                    <div class="hero-content text-white fade-in-up">
                        <h1 class="display-2 fw-bold mb-4 text-shadow text-white">
                            <i class="fas fa-phone me-3 text-warning pulse-animation"></i>
                            <?php echo e($contactPage->title ?? 'ติดต่อเรา'); ?>

                        </h1>
                        <p class="lead mb-5 fs-3 mx-auto text-white" style="max-width: 800px; line-height: 1.6;">
                            <?php echo e($contactPage->description ?? 'ติดต่อสอบถามข้อมูลเพิ่มเติม หรือจองโต๊ะล่วงหน้า'); ?>

                        </p>

                        <!-- Decorative Line -->
                        <div class="d-flex justify-content-center align-items-center mb-5">
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                            <div class="mx-3">
                                <i class="fas fa-phone text-warning fs-4"></i>
                            </div>
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                        </div>

                        <div class="d-inline-block bg-white bg-opacity-20 rounded-pill px-4 py-2 fs-5">
                            <i class="fas fa-clock me-2 text-warning"></i>
                            <span>พร้อมให้บริการทุกวัน</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Decorative Elements -->
    <div class="position-absolute top-0 start-0 w-100 h-100 overflow-hidden" style="z-index: 1;">
        <div class="position-absolute" style="top: 10%; left: -5%; width: 200px; height: 200px; background: rgba(255, 193, 7, 0.1); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div class="position-absolute" style="top: 60%; right: -5%; width: 150px; height: 150px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div class="position-absolute" style="bottom: 20%; left: 10%; width: 100px; height: 100px; background: rgba(139, 69, 19, 0.2); border-radius: 50%; animation: float 7s ease-in-out infinite;"></div>
    </div>

    <!-- Wave Pattern -->
    <div class="position-absolute bottom-0 start-0 w-100" style="z-index: 2;">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" style="height: 60px; width: 100%;">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="rgba(255,255,255,0.3)"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="rgba(255,255,255,0.2)"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="rgba(255,255,255,0.1)"></path>
        </svg>
    </div>
</section>

<!-- Contact Information Section -->
<section class="py-3">
    <div class="container">
        <div class="row g-3">
            <!-- Contact Details -->
            <div class="col-lg-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-primary text-white py-2">
                        <h5 class="mb-0">
                            <i class="fas fa-address-book me-2"></i>ข้อมูลติดต่อ
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Address -->
                        <?php if($contactPage->address || $restaurantInfo->address): ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt fa-2x" style="color: #e74c3c;"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1" style="color: #000000;">ที่อยู่ร้าน</h6>
                                    <p class="mb-0" style="color: #8B4513;">
                                        <?php echo e($contactPage->address ?? $restaurantInfo->address); ?>

                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Phone -->
                        <?php if($contactPage->phone || $restaurantInfo->phone): ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-phone fa-2x" style="color: #27ae60;"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1" style="color: #000000;">โทรศัพท์</h6>
                                    <p class="mb-0">
                                        <a href="tel:<?php echo e($contactPage->phone ?? $restaurantInfo->phone); ?>" class="text-decoration-none" style="color: #8B4513;">
                                            <?php echo e($contactPage->phone ?? $restaurantInfo->phone); ?>

                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Mobile -->
                        <?php if($contactPage->mobile || $restaurantInfo->mobile): ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-mobile-alt fa-2x" style="color: #3498db;"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1" style="color: #000000;">มือถือ</h6>
                                    <p class="mb-0">
                                        <a href="tel:<?php echo e($contactPage->mobile ?? $restaurantInfo->mobile); ?>" class="text-decoration-none" style="color: #8B4513;">
                                            <?php echo e($contactPage->mobile ?? $restaurantInfo->mobile); ?>

                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Email -->
                        <?php if($contactPage->email || $restaurantInfo->email): ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope fa-2x" style="color: #f39c12;"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1" style="color: #000000;">อีเมล</h6>
                                    <p class="mb-0">
                                        <a href="mailto:<?php echo e($contactPage->email ?? $restaurantInfo->email); ?>" class="text-decoration-none" style="color: #8B4513;">
                                            <?php echo e($contactPage->email ?? $restaurantInfo->email); ?>

                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Opening Hours -->
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-clock fa-2x" style="color: #9b59b6;"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1" style="color: #000000;">เวลาเปิด-ปิด</h6>
                                    <p class="mb-1" style="color: #8B4513;">
                                        <?php echo e($contactPage->formatted_opening_hours ?? $restaurantInfo->formatted_opening_hours ?? 'เปิดทุกวัน 08:00 - 20:00 น.'); ?>

                                    </p>
                                    <?php if($contactPage->formatted_open_days): ?>
                                    <small style="color: #8B4513; opacity: 0.7;"><?php echo e($contactPage->formatted_open_days); ?></small>
                                    <?php endif; ?>
                                    <?php if($contactPage->special_hours): ?>
                                    <div class="mt-2">
                                        <small style="color: #8B4513; opacity: 0.8;">
                                            <i class="fas fa-info-circle me-1"></i><?php echo e($contactPage->special_hours); ?>

                                        </small>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media & Additional Info -->
            <div class="col-lg-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-success text-white py-2">
                        <h5 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>ช่องทางติดต่ออื่นๆ
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Line -->
                        <?php if($contactPage->line_id || $restaurantInfo->line): ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fab fa-line fa-2x text-success"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">Line ID</h6>
                                    <p class="mb-0">
                                        <a href="https://line.me/ti/p/<?php echo e($contactPage->line_id ?? $restaurantInfo->line); ?>" 
                                           target="_blank" class="text-decoration-none">
                                            <?php echo e($contactPage->line_id ?? $restaurantInfo->line); ?>

                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Facebook -->
                        <?php if($contactPage->facebook || $restaurantInfo->facebook): ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fab fa-facebook fa-2x text-primary"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">Facebook</h6>
                                    <p class="mb-0">
                                        <a href="<?php echo e($contactPage->facebook ?? $restaurantInfo->facebook); ?>" 
                                           target="_blank" class="text-decoration-none">
                                            Facebook Page
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Instagram -->
                        <?php if($contactPage->instagram || $restaurantInfo->instagram): ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fab fa-instagram fa-2x text-danger"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">Instagram</h6>
                                    <p class="mb-0">
                                        <a href="<?php echo e($contactPage->instagram ?? $restaurantInfo->instagram); ?>" 
                                           target="_blank" class="text-decoration-none">
                                            Instagram Page
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Additional Info -->
                        <?php if($contactPage->additional_info): ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-info-circle fa-2x text-info"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">ข้อมูลเพิ่มเติม</h6>
                                    <p class="mb-0 text-muted">
                                        <?php echo nl2br(e($contactPage->additional_info)); ?>

                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Quick Contact Buttons -->
                        <div class="mt-3">
                            <h6 class="fw-bold mb-2">ติดต่อด่วน</h6>
                            <div class="d-flex flex-wrap gap-2">
                                <?php if($contactPage->phone || $restaurantInfo->phone): ?>
                                <a href="tel:<?php echo e($contactPage->phone ?? $restaurantInfo->phone); ?>" 
                                   class="btn btn-success btn-sm">
                                    <i class="fas fa-phone me-1"></i>โทรเลย
                                </a>
                                <?php endif; ?>
                                
                                <?php if($contactPage->line_id || $restaurantInfo->line): ?>
                                <a href="https://line.me/ti/p/<?php echo e($contactPage->line_id ?? $restaurantInfo->line); ?>" 
                                   target="_blank" class="btn btn-success btn-sm">
                                    <i class="fab fa-line me-1"></i>Line
                                </a>
                                <?php endif; ?>
                                
                                <?php if($contactPage->facebook || $restaurantInfo->facebook): ?>
                                <a href="<?php echo e($contactPage->facebook ?? $restaurantInfo->facebook); ?>" 
                                   target="_blank" class="btn btn-primary btn-sm">
                                    <i class="fab fa-facebook me-1"></i>Facebook
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- Location Images Section -->
<?php if($contactPage->location_image || $contactPage->interior_image || $contactPage->parking_image): ?>
<section class="py-3 bg-light">
    <div class="container">
        <h4 class="text-center text-primary mb-3">
            <i class="fas fa-images me-2"></i>รูปภาพร้าน
        </h4>
        <div class="row g-4">
            <?php if($contactPage->location_image): ?>
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm">
                    <img src="<?php echo e(asset('storage/' . $contactPage->location_image)); ?>"
                         alt="หน้าร้าน" class="card-img-top" style="height: 140px; object-fit: cover;">
                    <div class="card-body text-center py-2">
                        <h6 class="card-title mb-0">หน้าร้าน</h6>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if($contactPage->interior_image): ?>
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm">
                    <img src="<?php echo e(asset('storage/' . $contactPage->interior_image)); ?>"
                         alt="ภายในร้าน" class="card-img-top" style="height: 140px; object-fit: cover;">
                    <div class="card-body text-center py-2">
                        <h6 class="card-title mb-0">ภายในร้าน</h6>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if($contactPage->parking_image): ?>
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm">
                    <img src="<?php echo e(asset('storage/' . $contactPage->parking_image)); ?>"
                         alt="ที่จอดรถ" class="card-img-top" style="height: 140px; object-fit: cover;">
                    <div class="card-body text-center py-2">
                        <h6 class="card-title mb-0">ที่จอดรถ</h6>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Transportation & Parking Info -->
<?php if($contactPage->directions || $contactPage->parking_info || $contactPage->public_transport): ?>
<section class="py-5">
    <div class="container">
        <h2 class="text-center text-primary mb-5">
            <i class="fas fa-route me-2"></i>การเดินทาง
        </h2>
        <div class="row g-4">
            <?php if($contactPage->directions): ?>
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-map-signs fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">คำแนะนำการเดินทาง</h5>
                        <p class="card-text">
                            <?php echo nl2br(e($contactPage->directions)); ?>

                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if($contactPage->parking_info): ?>
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-parking fa-3x text-success mb-3"></i>
                        <h5 class="card-title">ที่จอดรถ</h5>
                        <p class="card-text">
                            <?php echo nl2br(e($contactPage->parking_info)); ?>

                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if($contactPage->public_transport): ?>
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-bus fa-3x text-info mb-3"></i>
                        <h5 class="card-title">ขนส่งสาธารณะ</h5>
                        <p class="card-text">
                            <?php echo nl2br(e($contactPage->public_transport)); ?>

                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Map Section -->
<?php if($contactPage->map_embed): ?>
<section class="py-3 bg-light">
    <div class="container">
        <h4 class="text-center text-primary mb-3">
            <i class="fas fa-map me-2"></i>แผนที่ร้าน
        </h4>
        <div class="row">
            <div class="col-12">
                <div class="map-container rounded shadow">
                    <?php echo $contactPage->map_embed; ?>

                </div>
            </div>
        </div>
    </div>
</section>
<?php else: ?>
<section class="py-3 bg-light">
    <div class="container">
        <h4 class="text-center text-primary mb-3">แผนที่ร้าน</h4>
        <div class="row">
            <div class="col-12">
                <div class="bg-white rounded p-5 text-center shadow-sm">
                    <i class="fas fa-map fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">แผนที่ Google Maps</h5>
                    <p class="text-muted">สามารถเพิ่ม Google Maps embed code ในหลังบ้าน</p>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<?php $__env->startPush('styles'); ?>
<style>
.hero-section {
    position: relative;
    overflow: hidden;
}

.contact-item {
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateX(5px);
}

.contact-icon {
    min-width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.contact-icon:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.2);
}

.contact-icon i {
    transition: all 0.3s ease;
}

.contact-icon:hover i {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.map-container iframe {
    width: 100%;
    height: 250px;
    border: 0;
    border-radius: 8px;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/contact/index.blade.php ENDPATH**/ ?>