@extends('layouts.app')

@section('title', 'เมนูอาหาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@push('styles')
<style>
/* Hero Section Styles */
.hero-section {
    min-height: 40vh;
    position: relative;
    overflow: hidden;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed;
    filter: brightness(0.8) contrast(1.1) saturate(1.2);
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.5) 0%,
        rgba(139, 69, 19, 0.7) 30%,
        rgba(218, 165, 32, 0.4) 70%,
        rgba(0, 0, 0, 0.6) 100%
    );
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
    height: 40vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.fade-in-up {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Input Styles */
.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
    border-color: #ffc107;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
    }

    .hero-section .container {
        height: 50vh;
    }

    .display-2 {
        font-size: 2.5rem;
    }

    .fs-3 {
        font-size: 1.2rem !important;
    }
}
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden" style="min-height: 40vh;">
    <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ asset('images/restaurant/background.jpg') }}'); background-size: cover; background-position: center;">
        <div class="container position-relative h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-10 mx-auto text-center">
                    <div class="fade-in-up">
                        <h1 class="display-2 fw-bold mb-4 text-white text-shadow">
                            <i class="fas fa-utensils me-3 text-warning pulse-animation"></i>
                            เมนูอาหาร
                        </h1>
                        <p class="lead mb-5 text-white fs-3 mx-auto" style="max-width: 800px; line-height: 1.6;">
                            ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม พร้อมเครื่องเทศครบเครื่อง
                        </p>

                        <div class="d-flex justify-content-center align-items-center mt-4">
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                            <div class="mx-3">
                                <i class="fas fa-utensils text-warning fs-4"></i>
                            </div>
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Decorative Elements -->
    <div class="position-absolute top-0 start-0 w-100 h-100 overflow-hidden" style="z-index: 1;">
        <div class="position-absolute" style="top: 10%; left: -5%; width: 200px; height: 200px; background: rgba(255, 193, 7, 0.1); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div class="position-absolute" style="top: 60%; right: -5%; width: 150px; height: 150px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div class="position-absolute" style="bottom: 20%; left: 10%; width: 100px; height: 100px; background: rgba(139, 69, 19, 0.2); border-radius: 50%; animation: float 7s ease-in-out infinite;"></div>
    </div>

    <!-- Wave Pattern -->
    <div class="position-absolute bottom-0 start-0 w-100" style="z-index: 2;">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" style="height: 60px; width: 100%;">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="rgba(255,255,255,0.3)"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="rgba(255,255,255,0.2)"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="rgba(255,255,255,0.1)"></path>
        </svg>
    </div>
</section>

<!-- Categories Filter -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h3 class="fw-bold text-primary mb-3">
                        <i class="fas fa-filter me-2"></i>เลือกหมวดหมู่
                    </h3>
                    <p class="text-muted">กรองเมนูตามหมวดหมู่ที่คุณสนใจ</p>
                </div>

                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="{{ route('menu.index') }}"
                       class="btn {{ !request('category') ? 'btn-primary' : 'btn-outline-primary' }} rounded-pill px-4 py-2 shadow-sm">
                        <i class="fas fa-th-large me-2"></i>ทั้งหมด
                        <span class="badge bg-light text-dark ms-2">{{ $allMenusCount ?? 0 }}</span>
                    </a>
                    @foreach($categories as $category)
                        <a href="{{ route('menu.category', $category->slug) }}"
                           class="btn {{ request('category') == $category->slug ? 'btn-primary' : 'btn-outline-primary' }} rounded-pill px-4 py-2 shadow-sm position-relative">
                            <i class="fas fa-utensils me-2"></i>{{ $category->name }}
                            <span class="badge bg-light text-dark ms-2">{{ $category->menuItems->count() }}</span>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Menu Section -->
@if($featuredMenus->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold text-primary mb-3">
                    <i class="fas fa-star text-warning me-2"></i>เมนูแนะนำ
                </h2>
                <p class="lead text-muted mt-4 fs-4">เมนูยอดนิยมที่ลูกค้าแนะนำ</p>
            </div>
        </div>

        <div class="row g-4">
            @foreach($featuredMenus as $index => $menu)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-lg menu-card" style="animation-delay: {{ $index * 0.1 }}s;">
                        <!-- Featured Badge -->
                        <div class="position-absolute top-0 end-0 z-index-1">
                            <div class="bg-warning text-dark px-3 py-2 rounded-bottom-start">
                                <i class="fas fa-crown me-1"></i>แนะนำ
                            </div>
                        </div>
                        
                        @if($menu->image)
                            @php
                                $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($menu->image);
                            @endphp
                            <div class="position-relative overflow-hidden">
                                <img src="{{ $imageUrl }}"
                                     class="card-img-top menu-image"
                                     alt="{{ $menu->name }}"
                                     style="height: 250px; object-fit: cover;"
                                     loading="lazy"
                                     onerror="this.onerror=null; this.src='{{ asset('images/menu/placeholder.svg') }}'; this.parentElement.classList.add('bg-light');">
                            </div>
                        @else
                            <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center position-relative"
                                 style="height: 250px; background: linear-gradient(135deg, #f8f9fa, #e9ecef);">
                                <img src="{{ asset('images/menu/placeholder.svg') }}"
                                     alt="ไม่มีรูปภาพ"
                                     class="opacity-75"
                                     style="width: 150px; height: 150px; object-fit: contain;">
                            </div>
                        @endif
                        
                        <div class="card-body d-flex flex-column">
                            <div class="text-center mb-3">
                                <h5 class="card-title text-primary fw-bold mb-2">{{ $menu->name }}</h5>
                                <span class="badge bg-primary rounded-pill">{{ $menu->category->name }}</span>
                            </div>
                            
                            <p class="card-text text-muted flex-grow-1 mb-3 text-center">{{ $menu->description }}</p>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="h4 text-primary fw-bold mb-0">{{ $menu->formatted_price }}</span>
                                    <a href="{{ route('menu.show', $menu->id) }}" class="btn btn-warning text-dark rounded-pill px-3 py-2">
                                        <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- All Menu Section -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold text-primary mb-3">
                    <i class="fas fa-list me-2 text-warning"></i>
                    เมนูอาหารทั้งหมด
                </h2>
                <p class="lead text-muted mt-4 fs-4">สำรวจเมนูอาหารหลากหลายรสชาติ</p>
            </div>
        </div>
        
        @if($menuItems->count() > 0)
            <div class="row g-4">
                @foreach($menuItems as $index => $menu)
                    <div class="col-lg-3 col-md-6">
                        <div class="card h-100 border-0 shadow menu-card-small" style="animation-delay: {{ $index * 0.05 }}s;">
                            @if($menu->image)
                                <div class="position-relative overflow-hidden">
                                    <img src="{{ asset('storage/' . $menu->image) }}"
                                         class="card-img-top menu-image-small"
                                         alt="{{ $menu->name }}"
                                         style="height: 200px; object-fit: cover; cursor: pointer; transition: transform 0.3s ease;"
                                         data-bs-toggle="modal"
                                         data-bs-target="#imageModal"
                                         data-image-src="{{ asset('storage/' . $menu->image) }}"
                                         data-image-alt="{{ $menu->name }}"
                                         loading="lazy"
                                         onerror="this.onerror=null; this.src='{{ asset('images/menu/placeholder.svg') }}'; this.parentElement.classList.add('bg-light');">
                                    @if($menu->is_featured)
                                        <div class="position-absolute top-0 end-0 p-2">
                                            <span class="badge bg-warning text-dark">
                                                แนะนำ
                                            </span>
                                        </div>
                                    @endif
                                </div>
                            @else
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center position-relative"
                                     style="height: 200px;">
                                    <img src="{{ asset('images/menu/placeholder.svg') }}"
                                         alt="ไม่มีรูปภาพ"
                                         class="opacity-75"
                                         style="width: 100px; height: 100px; object-fit: contain;">
                                </div>
                            @endif
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title text-primary fw-bold mb-2 text-center">{{ $menu->name }}</h6>
                                <p class="card-text text-muted small flex-grow-1 mb-3 text-center">
                                    {{ Str::limit($menu->description, 80) }}
                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 text-primary fw-bold mb-0">{{ $menu->formatted_price }}</span>
                                    <a href="{{ route('menu.show', $menu->id) }}" class="btn btn-warning text-dark btn-sm rounded-pill px-3">
                                        <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            

        @else
            <div class="text-center py-5">
                <h4 class="text-muted mb-3">ไม่พบเมนูอาหาร</h4>
                <p class="text-muted">ยังไม่มีเมนูอาหารในระบบ</p>
            </div>
        @endif
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img id="modalImage" src="" alt="" class="img-fluid rounded shadow-lg" style="max-height: 80vh;">
            </div>
        </div>
    </div>
</div>

<style>
.menu-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.menu-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.menu-card:hover .menu-image {
    transform: scale(1.05);
}

.menu-image {
    transition: transform 0.4s ease;
}

.menu-card-small {
    transition: all 0.3s ease;
}

.menu-card-small:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.menu-card-small:hover .menu-image-small {
    transform: scale(1.05);
}

.menu-image-small {
    transition: transform 0.3s ease;
}

.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color));
    border-radius: 1px;
}

.z-index-1 {
    z-index: 1;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.text-white-75 {
    color: rgba(255,255,255,0.9) !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Image Modal Styles */
#imageModal .modal-content {
    background: transparent !important;
}

#imageModal .btn-close-white {
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    opacity: 1;
}

#imageModal .btn-close-white:hover {
    background: rgba(255,255,255,1);
}

.menu-image-small:hover {
    transform: scale(1.05);
}

/* Image loading and error handling */
.card-img-top {
    background-color: #f8f9fa;
    background-image: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                      linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                      linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.card-img-top img {
    transition: opacity 0.3s ease;
}

.card-img-top img[src*="placeholder.svg"] {
    opacity: 0.6;
}

/* Responsive image improvements */
@media (max-width: 576px) {
    .menu-card-small .card-img-top {
        height: 180px !important;
    }

    .menu-card .card-img-top {
        height: 220px !important;
    }
}
</style>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle image modal
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');

    if (imageModal && modalImage) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imageSrc = button.getAttribute('data-image-src');
            const imageAlt = button.getAttribute('data-image-alt');

            modalImage.src = imageSrc;
            modalImage.alt = imageAlt;
        });

        // Close modal when clicking on the image
        modalImage.addEventListener('click', function() {
            const modalInstance = bootstrap.Modal.getInstance(imageModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modalInstance = bootstrap.Modal.getInstance(imageModal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    }
});
</script>
@endpush

@endsection
