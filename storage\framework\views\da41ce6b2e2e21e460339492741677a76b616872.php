<?php $__env->startSection('title', $category->name . ' - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>

<!-- Category Header -->
<section class="hero-section position-relative overflow-hidden" style="min-height: 40vh;">
    <?php
        $backgroundImageUrl = (isset($category->image) && $category->image)
            ? \App\Helpers\ImageHelper::getCategoryImageUrl($category->image)
            : asset('images/restaurant/background.jpg');
    ?>
    <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('<?php echo e($backgroundImageUrl); ?>'); background-size: cover; background-position: center;">
        <div class="container position-relative h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-10 mx-auto text-center">
                    <div class="fade-in-up">
                        <h1 class="display-2 fw-bold mb-4 text-white text-shadow">
                            <i class="fas fa-utensils me-3 text-warning pulse-animation"></i>
                            <?php echo e($category->name); ?>

                        </h1>
                        <?php if(isset($category->description) && $category->description): ?>
                            <p class="lead mb-5 text-white fs-3 mx-auto" style="max-width: 800px; line-height: 1.6;">
                                <?php echo e($category->description); ?>

                            </p>
                        <?php endif; ?>

                        <div class="d-flex justify-content-center align-items-center mt-4">
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                            <div class="mx-3">
                                <i class="fas fa-utensils text-warning fs-4"></i>
                            </div>
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Decorative Elements -->
    <div class="position-absolute top-0 start-0 w-100 h-100 overflow-hidden" style="z-index: 1;">
        <div class="position-absolute" style="top: 10%; left: -5%; width: 200px; height: 200px; background: rgba(255, 193, 7, 0.1); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div class="position-absolute" style="top: 60%; right: -5%; width: 150px; height: 150px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div class="position-absolute" style="bottom: 20%; left: 10%; width: 100px; height: 100px; background: rgba(139, 69, 19, 0.2); border-radius: 50%; animation: float 7s ease-in-out infinite;"></div>
    </div>

    <!-- Wave Pattern -->
    <div class="position-absolute bottom-0 start-0 w-100" style="z-index: 2;">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" style="height: 60px; width: 100%;">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="rgba(255,255,255,0.3)"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="rgba(255,255,255,0.2)"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="rgba(255,255,255,0.1)"></path>
        </svg>
    </div>
</section>

<!-- Breadcrumb -->
<section class="py-3 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-decoration-none">หน้าหลัก</a></li>
                <li class="breadcrumb-item"><a href="<?php echo e(route('menu.index')); ?>" class="text-decoration-none">เมนูอาหาร</a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e($category->name); ?></li>
            </ol>
        </nav>
    </div>
</section>

<!-- Menu Items -->
<section class="py-5">
    <div class="container">
        <?php if($menuItems->count() > 0): ?>
            <div class="row">
                <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg menu-item-card">
                            <?php if($item->image): ?>
                                <?php
                                    $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($item->image);
                                ?>
                                <!-- Debug: Image path = <?php echo e($item->image); ?> -->
                                <!-- Debug: Full URL = <?php echo e($imageUrl); ?> -->
                                <!-- Debug: Category = <?php echo e($item->category->name ?? 'N/A'); ?> -->
                                <div class="position-relative overflow-hidden" style="height: 250px; background: #f8f9fa;">
                                    <img src="<?php echo e($imageUrl); ?>"
                                         class="card-img-top h-100 w-100 menu-image-visible"
                                         style="object-fit: cover; transition: transform 0.3s ease; cursor: pointer; opacity: 1 !important; visibility: visible !important; display: block !important; position: relative !important; z-index: 10 !important;"
                                         alt="<?php echo e($item->name); ?>"
                                         loading="eager"
                                         data-bs-toggle="modal"
                                         data-bs-target="#imageModal"
                                         data-image-src="<?php echo e($imageUrl); ?>"
                                         data-image-alt="<?php echo e($item->name); ?>"
                                         onerror="console.error('❌ Card image failed to load:', this.src); this.style.display='none'; this.parentElement.innerHTML='<div class=\'d-flex align-items-center justify-content-center h-100 bg-light\'><i class=\'fas fa-image fa-3x text-muted\'></i></div>';"
                                         onload="console.log('✅ Card image loaded successfully:', this.src, 'Size:', this.naturalWidth + 'x' + this.naturalHeight);">
                                    <div class="position-absolute top-0 end-0 m-3">
                                        <?php if($item->is_featured): ?>
                                            <span class="badge bg-warning text-dark px-3 py-2">
                                                <i class="fas fa-star me-1"></i>แนะนำ
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if(isset($item->category)): ?>
                                        <div class="position-absolute top-0 start-0 m-3">
                                            <span class="badge bg-primary px-3 py-2">
                                                <?php echo e($item->category->name); ?>

                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <!-- Debug: No image for <?php echo e($item->name); ?> -->
                                <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 250px;">
                                    <div class="text-center">
                                        <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                        <p class="text-muted small mb-0">ไม่มีรูปภาพ</p>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title fw-bold text-primary mb-2 text-center"><?php echo e($item->name); ?></h5>

                                <div class="d-flex justify-content-between align-items-center mt-auto">
                                    <div class="price">
                                        <span class="h4 fw-bold text-success mb-0"><?php echo e(number_format($item->price)); ?> ฿</span>
                                    </div>
                                    <a href="<?php echo e(route('menu.show', $item->id)); ?>" class="btn btn-primary btn-sm px-3">
                                        <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <h3 class="text-muted">ยังไม่มีเมนูในหมวดหมู่นี้</h3>
                <p class="text-muted mb-4">กรุณาเลือกหมวดหมู่อื่น หรือกลับไปดูเมนูทั้งหมด</p>
                <a href="<?php echo e(route('menu.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>กลับไปดูเมนูทั้งหมด
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>



<!-- Image Modal - Simplified -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content" style="background: rgba(0,0,0,0.9); border: none;">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white" id="imageModalLabel">รูปอาหาร</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-3">
                <img id="modalImage"
                     src=""
                     alt=""
                     class="img-fluid rounded"
                     style="max-height: 70vh; max-width: 100%; width: auto; height: auto; object-fit: contain;">
                <div id="modalImageError" style="display: none;" class="text-white mt-3">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>ไม่สามารถโหลดรูปภาพได้</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Hero Section Styles */
.hero-section {
    position: relative;
    min-height: 40vh;
    display: flex;
    align-items: center;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-attachment: fixed;
}

.fade-in-up {
    animation: fadeInUp 1s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

/* Float Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Force Image Visibility - แก้ปัญหารูปหายเมื่อเลื่อนหน้า */
.menu-image-visible,
.card-img-top,
.menu-item-card img {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Override any lazy loading or intersection observer effects */
img[loading="lazy"],
img[data-src],
.lazy-load,
.fade-in,
.fade-out {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    transform: none !important;
}

/* Ensure card containers are always visible */
.menu-item-card,
.card {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Prevent any hiding animations on scroll */
.menu-item-card *,
.card * {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
}

/* Menu Card Styles */
.menu-item-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
    opacity: 1 !important;
    visibility: visible !important;
}

.menu-item-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
}

.menu-item-card .card-img-top {
    transition: transform 0.3s ease;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.menu-item-card .card-img-top:hover {
    transform: scale(1.05);
}

/* Force image visibility */
.menu-item-card img {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

/* Breadcrumb Styles */
.breadcrumb {
    background: rgba(255,255,255,0.9);
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    backdrop-filter: blur(10px);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--bs-primary);
    font-weight: bold;
}

/* Image Modal Styles - Simplified */
#imageModal .modal-content {
    background: rgba(0,0,0,0.9) !important;
    border: none !important;
}

#imageModal .btn-close-white {
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    opacity: 1;
}

#imageModal .btn-close-white:hover {
    background: rgba(255,255,255,1);
}

#modalImage {
    max-height: 70vh !important;
    max-width: 100% !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
}

#modalImageError {
    color: white !important;
    text-align: center !important;
}

/* Force all images to be visible - Override any conflicting styles */
.menu-item-card img,
.menu-item-card .card-img-top,
.card img,
.card .card-img-top {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Ensure card containers are visible */
.menu-item-card,
.card {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Override any lazy loading or intersection observer effects */
.menu-item-card img[loading="lazy"],
.card img[loading="lazy"] {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 30vh;
    }

    .display-2 {
        font-size: 2.5rem;
    }

    .lead {
        font-size: 1.2rem;
    }

    /* Ensure mobile images are visible */
    .menu-item-card img {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Force image visibility - แก้ปัญหารูปหายเมื่อเลื่อนหน้า
    function ensureImagesVisible() {
        // Force all images to be visible
        const images = document.querySelectorAll('.menu-item-card img, .card-img-top, .menu-image-visible, img');
        console.log(`🔍 Found ${images.length} images to check`);

        images.forEach(function(img, index) {
            // Skip if not a menu image
            if (!img.src || img.src.includes('data:') || img.classList.contains('checked')) {
                return;
            }

            img.classList.add('checked'); // Mark as checked

            console.log(`🖼️ Checking image ${index + 1}:`, {
                src: img.src,
                naturalWidth: img.naturalWidth,
                naturalHeight: img.naturalHeight,
                complete: img.complete,
                visible: img.style.display !== 'none'
            });

            img.style.opacity = '1';
            img.style.visibility = 'visible';
            img.style.display = 'block';
            img.style.position = 'relative';
            img.style.zIndex = '10';
            img.style.transform = 'none';
            img.style.transition = 'transform 0.3s ease';

            // If image fails to load, show placeholder
            img.onerror = function() {
                if (!this.dataset.errorHandled) {
                    this.dataset.errorHandled = 'true';
                    console.error('❌ Image failed to load, replacing with placeholder:', this.src);
                    const placeholder = document.createElement('div');
                    placeholder.className = 'card-img-top d-flex align-items-center justify-content-center bg-light';
                    placeholder.style.height = '250px';
                    placeholder.innerHTML = '<div class="text-center"><i class="fas fa-image fa-3x text-muted mb-2"></i><p class="text-muted small mb-0">รูปไม่พบ</p></div>';
                    this.parentNode.replaceChild(placeholder, this);
                }
            };

            // Test if image is actually loaded
            if (img.complete && img.naturalWidth === 0) {
                console.warn('⚠️ Image appears broken:', img.src);
                img.onerror();
            }
        });

        // Ensure cards are visible
        const cards = document.querySelectorAll('.menu-item-card, .card');
        cards.forEach(function(card) {
            card.style.opacity = '1';
            card.style.visibility = 'visible';
            card.style.display = 'block';
            card.style.transform = 'none';
        });

        // Remove any fade classes that might hide elements
        const fadeElements = document.querySelectorAll('.fade-out, .hidden, .invisible');
        fadeElements.forEach(function(element) {
            element.classList.remove('fade-out', 'hidden', 'invisible');
            element.style.opacity = '1';
            element.style.visibility = 'visible';
            element.style.display = 'block';
        });

        console.log('✅ Images visibility check completed');
    }

    // Run immediately
    ensureImagesVisible();

    // Run again after a short delay
    setTimeout(ensureImagesVisible, 100);

    // Run on scroll to ensure images stay visible
    window.addEventListener('scroll', function() {
        ensureImagesVisible();
    });

    // Run on resize
    window.addEventListener('resize', function() {
        ensureImagesVisible();
    });

    // Run periodically to ensure images stay visible
    setInterval(ensureImagesVisible, 1000);

    // Handle image modal - Simple approach
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');

    console.log('🔍 Modal elements found:', {
        imageModal: !!imageModal,
        modalImage: !!modalImage,
        bootstrapAvailable: typeof bootstrap !== 'undefined'
    });

    if (imageModal && modalImage) {
        // Use Bootstrap's built-in modal events
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget; // Button that triggered the modal
            const imageSrc = button.getAttribute('data-image-src');
            const imageAlt = button.getAttribute('data-image-alt');

            console.log('� Modal opening with image:', {
                src: imageSrc,
                alt: imageAlt,
                button: button
            });

            // Update modal title
            const modalTitle = document.getElementById('imageModalLabel');
            if (modalTitle) {
                modalTitle.textContent = imageAlt || 'รูปอาหาร';
            }

            // Hide error message
            const errorElement = document.getElementById('modalImageError');
            if (errorElement) {
                errorElement.style.display = 'none';
            }

            // Set image
            if (imageSrc) {
                modalImage.style.display = 'block';
                modalImage.alt = imageAlt || 'รูปอาหาร';

                // Handle image load error
                modalImage.onerror = function() {
                    console.error('❌ Failed to load modal image:', imageSrc);
                    this.style.display = 'none';
                    if (errorElement) {
                        errorElement.style.display = 'block';
                    }
                };

                // Handle successful image load
                modalImage.onload = function() {
                    console.log('✅ Modal image loaded successfully');
                    this.style.display = 'block';
                    if (errorElement) {
                        errorElement.style.display = 'none';
                    }
                };

                // Set image source
                modalImage.src = imageSrc;
            }
        });

        // Clear image when modal is hidden
        imageModal.addEventListener('hidden.bs.modal', function () {
            modalImage.src = '';
            modalImage.style.display = 'none';
            const errorElement = document.getElementById('modalImageError');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/menu-category.blade.php ENDPATH**/ ?>