@extends('layouts.app')

@section('title', 'ข่าวสารประชาสัมพันธ์ - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@push('styles')
<style>
/* Hero Section Styles */
.hero-section {
    min-height: 40vh;
    position: relative;
    overflow: hidden;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed;
    filter: brightness(0.8) contrast(1.1) saturate(1.2);
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.5) 0%,
        rgba(139, 69, 19, 0.7) 30%,
        rgba(218, 165, 32, 0.4) 70%,
        rgba(0, 0, 0, 0.6) 100%
    );
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
    height: 40vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Force white color for news title */
.news-title {
    color: #ffffff !important;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.fade-in-up {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Button Styles */
.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
    }

    .hero-section .container {
        height: 50vh;
    }

    .display-2 {
        font-size: 2.5rem;
    }

    .fs-3 {
        font-size: 1.2rem !important;
    }
}
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ asset('images/restaurant/background.jpg') }}'); background-size: cover; background-position: center;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-10 mx-auto text-center">
                    <div class="hero-content text-white fade-in-up">
                        <h1 class="display-2 fw-bold mb-4 text-shadow news-title">
                            <i class="fas fa-newspaper me-3 text-warning pulse-animation"></i>
                            ข่าวสารประชาสัมพันธ์
                        </h1>


                        <!-- Decorative Line -->
                        <div class="d-flex justify-content-center align-items-center mb-4">
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                            <div class="mx-3">
                                <i class="fas fa-newspaper text-warning fs-4"></i>
                            </div>
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Decorative Elements -->
    <div class="position-absolute top-0 start-0 w-100 h-100 overflow-hidden" style="z-index: 1;">
        <div class="position-absolute" style="top: 10%; left: -5%; width: 200px; height: 200px; background: rgba(255, 193, 7, 0.1); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div class="position-absolute" style="top: 60%; right: -5%; width: 150px; height: 150px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div class="position-absolute" style="bottom: 20%; left: 10%; width: 100px; height: 100px; background: rgba(139, 69, 19, 0.2); border-radius: 50%; animation: float 7s ease-in-out infinite;"></div>
    </div>

    <!-- Wave Pattern -->
    <div class="position-absolute bottom-0 start-0 w-100" style="z-index: 2;">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" style="height: 60px; width: 100%;">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="rgba(255,255,255,0.3)"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="rgba(255,255,255,0.2)"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="rgba(255,255,255,0.1)"></path>
        </svg>
    </div>
</section>

<!-- Featured News Section -->
@if($featuredNews->count() > 0)
<section class="py-5 bg-white">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-star text-warning me-3"></i>ข่าวสารแนะนำ
                </h2>
                <p class="lead text-muted">ข่าวสารสำคัญที่คุณไม่ควรพลาด</p>
            </div>
        </div>
        
        <div class="row g-4">
            @foreach($featuredNews as $index => $item)
                <div class="col-lg-4 col-md-6">
                    <article class="card h-100 border-0 shadow-lg news-card" style="animation-delay: {{ $index * 0.1 }}s;">
                        @if($item->image)
                            @php
                                $imageUrl = \App\Helpers\ImageHelper::getImageUrl($item->image, 'images/menu/placeholder.svg');
                            @endphp
                            <div class="position-relative overflow-hidden">
                                <img src="{{ $imageUrl }}"
                                     class="card-img-top news-image"
                                     alt="{{ $item->title }}"
                                     style="height: 250px; object-fit: cover; cursor: pointer; transition: transform 0.3s ease;"
                                     data-bs-toggle="modal"
                                     data-bs-target="#imageModal"
                                     data-image-src="{{ $imageUrl }}"
                                     data-image-alt="{{ $item->title }}">
                                <div class="position-absolute top-0 end-0 p-3">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>แนะนำ
                                    </span>
                                </div>
                            </div>
                        @else
                            <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center" 
                                 style="height: 250px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                <i class="fas fa-newspaper fa-4x text-white opacity-50"></i>
                            </div>
                        @endif
                        
                        <div class="card-body d-flex flex-column">
                            <div class="mb-3">
                                <h5 class="card-title text-primary fw-bold mb-2">{{ $item->title }}</h5>
                                <p class="card-text text-muted">{{ $item->short_content }}</p>
                            </div>
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $item->formatted_published_at }}
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ $item->creator->name }}
                                    </small>
                                </div>
                                <a href="{{ route('news.show', $item->id) }}" class="btn btn-primary w-100">
                                    <i class="fas fa-arrow-right me-2"></i>อ่านต่อ
                                </a>
                            </div>
                        </div>
                    </article>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- All News Section -->
<section class="py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-list me-3 text-primary"></i>ข่าวสารทั้งหมด
                </h2>
            </div>
        </div>
        
        @if($news->count() > 0)
            <div class="row g-4">
                @foreach($news as $index => $item)
                    <div class="col-lg-6 col-md-6">
                        <article class="card h-100 border-0 shadow news-card-small" style="animation-delay: {{ $index * 0.05 }}s;">
                            <div class="row g-0 h-100">
                                <div class="col-md-4">
                                    @if($item->image)
                                        @php
                                            $imageUrl = \App\Helpers\ImageHelper::getImageUrl($item->image, 'images/menu/placeholder.svg');
                                        @endphp
                                        <img src="{{ $imageUrl }}"
                                             class="img-fluid rounded-start h-100"
                                             alt="{{ $item->title }}"
                                             style="object-fit: cover; cursor: pointer; transition: transform 0.3s ease;"
                                             data-bs-toggle="modal"
                                             data-bs-target="#imageModal"
                                             data-image-src="{{ $imageUrl }}"
                                             data-image-alt="{{ $item->title }}">
                                    @else
                                        <div class="bg-light h-100 d-flex align-items-center justify-content-center rounded-start">
                                            <i class="fas fa-newspaper fa-2x text-muted"></i>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-8">
                                    <div class="card-body d-flex flex-column h-100">
                                        <div>
                                            <h6 class="card-title text-primary fw-bold mb-2">{{ $item->title }}</h6>
                                            <p class="card-text text-muted small">{{ Str::limit($item->short_content, 100) }}</p>
                                        </div>
                                        
                                        <div class="mt-auto">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ $item->published_at->format('d/m/Y') }}
                                                </small>
                                                @if($item->is_featured)
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-star me-1"></i>แนะนำ
                                                    </span>
                                                @endif
                                            </div>
                                            <a href="{{ route('news.show', $item->id) }}" class="btn btn-outline-primary btn-sm">
                                                อ่านต่อ <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($news->hasPages())
                <div class="row mt-5">
                    <div class="col-12 d-flex justify-content-center">
                        {{ $news->links() }}
                    </div>
                </div>
            @endif
        @else
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-5x text-muted opacity-50 mb-4"></i>
                <h4 class="text-muted mb-3">ยังไม่มีข่าวสาร</h4>
                <p class="text-muted">กรุณาติดตามข่าวสารใหม่ๆ ในอนาคต</p>
            </div>
        @endif
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img id="modalImage" src="" alt="" class="img-fluid rounded shadow-lg" style="max-height: 80vh;">
            </div>
        </div>
    </div>
</div>

<style>
.news-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.news-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.news-card:hover .news-image {
    transform: scale(1.05);
}

.news-image {
    transition: transform 0.4s ease;
}

.news-card-small {
    transition: all 0.3s ease;
}

.news-card-small:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.text-white-75 {
    color: rgba(255,255,255,0.9) !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Image Modal Styles */
#imageModal .modal-content {
    background: transparent !important;
}

#imageModal .btn-close-white {
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    opacity: 1;
}

#imageModal .btn-close-white:hover {
    background: rgba(255,255,255,1);
}

.news-image:hover,
.img-fluid:hover {
    transform: scale(1.05);
}
</style>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle image modal
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');

    if (imageModal && modalImage) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imageSrc = button.getAttribute('data-image-src');
            const imageAlt = button.getAttribute('data-image-alt');

            modalImage.src = imageSrc;
            modalImage.alt = imageAlt;
        });

        // Close modal when clicking on the image
        modalImage.addEventListener('click', function() {
            const modalInstance = bootstrap.Modal.getInstance(imageModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modalInstance = bootstrap.Modal.getInstance(imageModal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    }
});
</script>
@endpush

@endsection
